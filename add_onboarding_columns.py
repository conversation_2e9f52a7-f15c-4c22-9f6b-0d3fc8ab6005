#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add onboarding columns to the existing users table.
This is a one-time migration script.
"""

import sqlite3
import os

def add_onboarding_columns():
    # Path to the database
    db_path = os.path.join('instance', 'database.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add onboarding_completed column
        if 'onboarding_completed' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN onboarding_completed BOOLEAN DEFAULT 0")
            print("Added onboarding_completed column")
        else:
            print("onboarding_completed column already exists")
        
        # Add grade_level column
        if 'grade_level' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN grade_level VARCHAR(10)")
            print("Added grade_level column")
        else:
            print("grade_level column already exists")
        
        # Add subjects_taken column
        if 'subjects_taken' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN subjects_taken TEXT")
            print("Added subjects_taken column")
        else:
            print("subjects_taken column already exists")
        
        # Add subject_confidence column
        if 'subject_confidence' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN subject_confidence TEXT")
            print("Added subject_confidence column")
        else:
            print("subject_confidence column already exists")
        
        # Add onboarding_completed_at column
        if 'onboarding_completed_at' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN onboarding_completed_at DATETIME")
            print("Added onboarding_completed_at column")
        else:
            print("onboarding_completed_at column already exists")
        
        # Commit the changes
        conn.commit()
        print("Successfully added onboarding columns to users table")
        
    except Exception as e:
        print(f"Error adding columns: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    add_onboarding_columns()
