{% extends "base.html" %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Welcome back, {{ user.username }}!</h1>
                    <p class="text-gray-600 mt-1">Here's your personalized learning dashboard</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Grade Level</p>
                        <p class="font-semibold text-gray-900">{{ user.grade_level }}</p>
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl">
                            {% if user.grade_level == 'PSLE' %}📚
                            {% elif user.grade_level == 'J1' %}🎓
                            {% elif user.grade_level == 'J2' %}🏆
                            {% else %}👤{% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- User Subjects Overview -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Your Subjects</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for subject_code in user_subjects %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">{{ subject_display_names[subject_code] }}</h3>
                            <p class="text-sm text-gray-500">Confidence Level</p>
                        </div>
                        <div class="flex items-center">
                            {% set confidence = user_confidence.get(subject_code, 3) %}
                            {% for i in range(1, 6) %}
                                {% if i <= confidence %}
                                    <span class="text-yellow-400">⭐</span>
                                {% else %}
                                    <span class="text-gray-300">⭐</span>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Recommended Problems Section -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900">Recommended Problems</h2>
                                <p class="text-gray-600 text-sm mt-1">Problems tailored to your subjects and skill level</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                    {{ recommended_problems|length }} problems
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        {% if recommended_problems %}
                            <div class="space-y-4">
                                {% for question in recommended_problems %}
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-3 mb-2">
                                                <h3 class="font-medium text-gray-900">
                                                    {% if question.title %}
                                                        {{ question.title }}
                                                    {% else %}
                                                        Question #{{ question.id }}
                                                    {% endif %}
                                                </h3>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {{ question.subject_name }}
                                                </span>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    {{ question.topic_name }}
                                                </span>
                                            </div>
                                            
                                            {% if question.description %}
                                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                                {{ question.description[:150] }}{% if question.description|length > 150 %}...{% endif %}
                                            </p>
                                            {% endif %}
                                            
                                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                    {{ question.parts|length }} part{{ 's' if question.parts|length != 1 else '' }}
                                                </span>
                                                <span class="flex items-center">
                                                    <span class="mr-1">📊</span>
                                                    Your confidence: {{ question.user_confidence }}/5
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="ml-4">
                                            <a href="{{ url_for('load_question', question_id=question.id) }}" 
                                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                                                Start Problem
                                                <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <div class="mt-6 text-center">
                                <a href="{{ url_for('vault') }}" 
                                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Browse All Problems
                                    <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                </a>
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <div class="text-6xl mb-4">📚</div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No recommendations yet</h3>
                                <p class="text-gray-600 mb-4">We're working on finding the perfect problems for you based on your subjects.</p>
                                <a href="{{ url_for('vault') }}" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                    Explore Problem Vault
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Stats -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Subjects</span>
                            <span class="font-semibold text-gray-900">{{ user_subjects|length }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Grade Level</span>
                            <span class="font-semibold text-gray-900">{{ user.grade_level }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Member Since</span>
                            <span class="font-semibold text-gray-900">
                                {% if user.onboarding_completed_at %}
                                    {{ user.onboarding_completed_at.strftime('%b %Y') }}
                                {% else %}
                                    Recently
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ url_for('vault') }}" 
                           class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                            <span class="text-2xl mr-3">🏛️</span>
                            <div>
                                <p class="font-medium text-gray-900">Problem Vault</p>
                                <p class="text-sm text-gray-600">Browse all problems</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('groups') }}" 
                           class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                            <span class="text-2xl mr-3">👥</span>
                            <div>
                                <p class="font-medium text-gray-900">Study Groups</p>
                                <p class="text-sm text-gray-600">Join or create groups</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('list_problemsets') }}" 
                           class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                            <span class="text-2xl mr-3">📋</span>
                            <div>
                                <p class="font-medium text-gray-900">Problem Sets</p>
                                <p class="text-sm text-gray-600">Curated collections</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
