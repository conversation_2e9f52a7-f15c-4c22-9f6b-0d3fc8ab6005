from flask import request, jsonify, session, flash, redirect, url_for, render_template
from datetime import datetime
import json
from models import db, User
from .utils import login_required, error_logger, app_logger

def register_onboarding_routes(app, db, session):
    
    @app.route("/onboarding")
    @login_required
    def onboarding():
        """Display the onboarding form"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)
        
        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('login'))
        
        # If user has already completed onboarding, redirect to vault
        if user.onboarding_completed:
            return redirect(url_for('vault'))
        
        return render_template('onboarding.html')
    
    @app.route("/complete_onboarding", methods=['POST'])
    @login_required
    def complete_onboarding():
        """Complete the onboarding process"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)
        
        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('login'))
        
        # If user has already completed onboarding, redirect to vault
        if user.onboarding_completed:
            return redirect(url_for('vault'))
        
        try:
            # Get form data
            grade_level = request.form.get('grade_level')
            subjects_taken_json = request.form.get('subjects_taken')
            subject_confidence_json = request.form.get('subject_confidence')
            
            # Validate required fields
            if not grade_level:
                flash('Please select your grade level.', 'error')
                return redirect(url_for('onboarding'))
            
            if not subjects_taken_json:
                flash('Please select at least one subject.', 'error')
                return redirect(url_for('onboarding'))
            
            # Parse JSON data
            try:
                subjects_taken = json.loads(subjects_taken_json)
                subject_confidence = json.loads(subject_confidence_json) if subject_confidence_json else {}
            except json.JSONDecodeError:
                flash('Invalid data format. Please try again.', 'error')
                return redirect(url_for('onboarding'))
            
            # Validate grade level
            if grade_level not in ['PSLE', 'J1', 'J2']:
                flash('Invalid grade level selected.', 'error')
                return redirect(url_for('onboarding'))
            
            # Validate subjects based on grade level
            valid_subjects = get_valid_subjects_for_grade(grade_level)
            invalid_subjects = [s for s in subjects_taken if s not in valid_subjects]
            
            if invalid_subjects:
                flash(f'Invalid subjects selected: {", ".join(invalid_subjects)}', 'error')
                return redirect(url_for('onboarding'))
            
            # Validate confidence levels
            for subject, confidence in subject_confidence.items():
                if not isinstance(confidence, int) or confidence < 1 or confidence > 5:
                    flash(f'Invalid confidence level for {subject}.', 'error')
                    return redirect(url_for('onboarding'))
            
            # Complete onboarding
            user.complete_onboarding(grade_level, subjects_taken, subject_confidence)
            db.session.commit()
            
            app_logger.info(f"User {user.username} completed onboarding - Grade: {grade_level}, Subjects: {subjects_taken}")
            
            flash('Welcome to Vast! Your profile has been set up successfully.', 'success')
            return redirect(url_for('vault'))
            
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error completing onboarding for user {user_id}: {str(e)}")
            flash('An error occurred while setting up your profile. Please try again.', 'error')
            return redirect(url_for('onboarding'))
    
    @app.route("/api/onboarding/subjects/<grade>")
    @login_required
    def get_subjects_for_grade(grade):
        """API endpoint to get available subjects for a grade level"""
        try:
            subjects = get_valid_subjects_for_grade(grade)
            return jsonify({
                'status': 'success',
                'subjects': subjects
            })
        except Exception as e:
            error_logger.exception(f"Error getting subjects for grade {grade}: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'Failed to get subjects'
            }), 500

def get_valid_subjects_for_grade(grade):
    """Get valid subjects for a given grade level"""
    subject_mapping = {
        'PSLE': [
            'psle-math',
            'psle-science',
            'psle-english',
            'psle-chinese'
        ],
        'J1': [
            'h2-physics',
            'h2-chemistry',
            'h2-mathematics',
            'h1-physics',
            'h1-chemistry',
            'h1-mathematics'
        ],
        'J2': [
            'h2-physics',
            'h2-chemistry',
            'h2-mathematics',
            'h1-physics',
            'h1-chemistry',
            'h1-mathematics'
        ]
    }
    
    return subject_mapping.get(grade, [])

def check_onboarding_required(user):
    """Check if user needs to complete onboarding"""
    return not user.onboarding_completed if user else True
