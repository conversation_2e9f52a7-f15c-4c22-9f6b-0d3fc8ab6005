"""fixed typo in option table

Revision ID: ea2d76502cef
Revises: 8d611741e590
Create Date: 2025-05-13 23:16:37.740988

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ea2d76502cef'
down_revision = '8d611741e590'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('mcqs')
    with op.batch_alter_table('options', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_correct', sa.<PERSON>(), nullable=False))
        batch_op.drop_column('is_corect')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('options', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_corect', sa.BOOLEAN(), nullable=False))
        batch_op.drop_column('is_correct')

    op.create_table('mcqs',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
