"""Add pdf_attachment column to problemsets table

Revision ID: ad69c818d57d
Revises: 
Create Date: 2025-05-06 22:43:33.074737

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ad69c818d57d'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('problemsets', schema=None) as batch_op:
        batch_op.add_column(sa.Column('pdf_attachment', sa.String(length=255), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('problemsets', schema=None) as batch_op:
        batch_op.drop_column('pdf_attachment')

    # ### end Alembic commands ###
