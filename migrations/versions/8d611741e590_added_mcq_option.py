"""added mcq option

Revision ID: 8d611741e590
Revises: ad69c818d57d
Create Date: 2025-05-13 14:15:52.244213

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8d611741e590'
down_revision = 'ad69c818d57d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('parts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('input_type', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('parts', schema=None) as batch_op:
        batch_op.drop_column('input_type')

    # ### end Alembic commands ###
